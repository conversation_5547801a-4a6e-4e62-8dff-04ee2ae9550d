#!/usr/bin/env python3
"""
从mendeleev库中获取元素的氧化态信息，并添加到element_data.xlsx文件中
"""

import pandas as pd
from mendeleev import element
import numpy as np

def get_oxidation_states(element_symbol):
    """
    获取元素的氧化态

    Args:
        element_symbol (str): 元素符号，如 'H', 'He', 'Li' 等

    Returns:
        str: 氧化态列表的字符串表示，如果获取失败返回 'N/A'
    """
    try:
        elem = element(element_symbol)

        # 首先尝试使用 oxistates 属性（这是直接的列表）
        if hasattr(elem, 'oxistates') and elem.oxistates:
            ox_states = elem.oxistates
            ox_states_str = [str(state) for state in ox_states]
            return ', '.join(ox_states_str)

        # 如果没有 oxistates，尝试 oxidation_states 方法
        if hasattr(elem, 'oxidation_states'):
            ox_states = elem.oxidation_states()
            if ox_states and len(ox_states) > 0:
                ox_states_str = [str(state) for state in ox_states]
                return ', '.join(ox_states_str)

        # 如果都没有，返回 N/A
        return 'N/A'

    except Exception as e:
        print(f"获取元素 {element_symbol} 的氧化态时出错: {e}")
        return 'N/A'

def add_oxidation_states_to_excel(file_path):
    """
    读取Excel文件，为A_site和B_site两个工作表添加氧化态列
    
    Args:
        file_path (str): Excel文件路径
    """
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"发现工作表: {excel_file.sheet_names}")
        
        # 创建一个字典来存储更新后的数据
        updated_sheets = {}
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n处理工作表: {sheet_name}")
            
            # 读取当前工作表
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"工作表 {sheet_name} 的列: {list(df.columns)}")
            print(f"工作表 {sheet_name} 的形状: {df.shape}")
            
            # 显示前几行数据
            print(f"前5行数据:")
            print(df.head())
            
            # 寻找包含元素符号的列
            element_column = None
            for col in df.columns:
                if any(keyword in str(col).lower() for keyword in ['element', 'symbol', '元素']):
                    element_column = col
                    break
            
            if element_column is None:
                # 如果没有找到明确的元素列，尝试第一列
                element_column = df.columns[0]
                print(f"未找到明确的元素列，使用第一列: {element_column}")
            else:
                print(f"找到元素列: {element_column}")
            
            # 检查是否已经存在氧化态列
            oxidation_column_name = 'Oxidation_States'
            if oxidation_column_name in df.columns:
                print(f"氧化态列已存在，将更新数据")
            else:
                print(f"添加新的氧化态列: {oxidation_column_name}")
            
            # 为每个元素获取氧化态
            oxidation_states = []
            for idx, row in df.iterrows():
                element_symbol = str(row[element_column]).strip()
                if pd.isna(element_symbol) or element_symbol == '' or element_symbol == 'nan':
                    oxidation_states.append('N/A')
                else:
                    ox_states = get_oxidation_states(element_symbol)
                    oxidation_states.append(ox_states)
                    print(f"元素 {element_symbol}: {ox_states}")
            
            # 添加氧化态列
            df[oxidation_column_name] = oxidation_states
            
            # 保存更新后的数据框
            updated_sheets[sheet_name] = df
        
        # 将所有工作表写回Excel文件
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            for sheet_name, df in updated_sheets.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"\n成功更新文件: {file_path}")
        
        # 显示更新后的结果
        for sheet_name, df in updated_sheets.items():
            print(f"\n工作表 {sheet_name} 更新后的列: {list(df.columns)}")
            print(f"更新后的前5行数据:")
            print(df.head())
            
    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    file_path = 'element_data.xlsx'
    
    print("开始处理element_data.xlsx文件...")
    print("=" * 50)
    
    # 检查文件是否存在
    import os
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return
    
    # 添加氧化态信息
    add_oxidation_states_to_excel(file_path)
    
    print("=" * 50)
    print("处理完成!")

if __name__ == "__main__":
    main()
