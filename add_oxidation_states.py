#!/usr/bin/env python3
"""
从mendeleev库中获取元素的氧化态信息，并添加到element_data.xlsx文件中
"""

import pandas as pd
from mendeleev import element
import numpy as np

def get_oxidation_states(element_symbol):
    """
    获取元素的氧化态

    Args:
        element_symbol (str): 元素符号，如 'H', 'He', 'Li' 等

    Returns:
        str: 氧化态列表的字符串表示，如果获取失败返回 'N/A'
    """
    try:
        elem = element(element_symbol)

        # 首先尝试使用 oxistates 属性（这是直接的列表）
        if hasattr(elem, 'oxistates') and elem.oxistates:
            ox_states = elem.oxistates
            ox_states_str = [str(state) for state in ox_states]
            return ', '.join(ox_states_str)

        # 如果没有 oxistates，尝试 oxidation_states 方法
        if hasattr(elem, 'oxidation_states'):
            ox_states = elem.oxidation_states()
            if ox_states and len(ox_states) > 0:
                ox_states_str = [str(state) for state in ox_states]
                return ', '.join(ox_states_str)

        # 如果都没有，返回 N/A
        return 'N/A'

    except Exception as e:
        print(f"获取元素 {element_symbol} 的氧化态时出错: {e}")
        return 'N/A'

def get_additional_properties(element_symbol, site_type):
    """
    获取元素的额外属性

    Args:
        element_symbol (str): 元素符号
        site_type (str): 位点类型 ('A_site' 或 'B_site')

    Returns:
        dict: 包含各种属性的字典
    """
    try:
        elem = element(element_symbol)
        properties = {}

        # 共价半径 (Pyykkö)
        properties['covalent_radius_pyykko'] = getattr(elem, 'covalent_radius_pyykko', None)
        if properties['covalent_radius_pyykko'] is not None:
            try:
                properties['covalent_radius_pyykko'] = f"{float(properties['covalent_radius_pyykko']):.2f}"
            except (ValueError, TypeError):
                properties['covalent_radius_pyykko'] = 'N/A'
        else:
            properties['covalent_radius_pyykko'] = 'N/A'

        # 电子亲合能
        properties['electron_affinity'] = getattr(elem, 'electron_affinity', None)
        if properties['electron_affinity'] is not None:
            try:
                properties['electron_affinity'] = f"{float(properties['electron_affinity']):.2f}"
            except (ValueError, TypeError):
                properties['electron_affinity'] = 'N/A'
        else:
            properties['electron_affinity'] = 'N/A'

        # 电离能 - 根据位点类型获取不同数量
        ionization_values = []
        try:
            # 使用mendeleev库的_ionization_energies列表（注意下划线）
            ie_list = getattr(elem, '_ionization_energies', None)
            if ie_list is not None and len(ie_list) > 0:
                # 直接从列表中获取电离能，按顺序：第一、第二、第三等
                max_needed = 5 if site_type == 'B_site' else 3  # B位需要5个，A位需要3个
                for i in range(min(len(ie_list), max_needed)):
                    try:
                        ie_value = ie_list[i]
                        if ie_value is not None:
                            ionization_values.append(float(ie_value))
                        else:
                            break  # 如果遇到None值，停止获取后续电离能
                    except (ValueError, TypeError, IndexError):
                        break

            # 如果_ionization_energies列表为空或不存在，尝试其他方法
            if not ionization_values:
                # 尝试ionenergy属性（第一电离能的快捷方式）
                first_ie = getattr(elem, 'ionenergy', None)
                if first_ie is not None:
                    try:
                        ionization_values.append(float(first_ie))
                    except (ValueError, TypeError):
                        pass
        except Exception as e:
            print(f"获取{element_symbol}电离能时出错: {e}")

        if ionization_values:
            if site_type == 'A_site':
                # A位要第一、第二、第三电离能
                for i in range(1, 4):
                    key = f'ionization_energy_{i}'
                    if len(ionization_values) >= i:
                        try:
                            properties[key] = f"{float(ionization_values[i-1]):.2f}"
                        except (ValueError, TypeError):
                            properties[key] = 'N/A'
                    else:
                        properties[key] = 'N/A'
            else:  # B_site
                # B位要第一到第五电离能
                for i in range(1, 6):
                    key = f'ionization_energy_{i}'
                    if len(ionization_values) >= i:
                        try:
                            properties[key] = f"{float(ionization_values[i-1]):.2f}"
                        except (ValueError, TypeError):
                            properties[key] = 'N/A'
                    else:
                        properties[key] = 'N/A'
        else:
            # 如果没有电离能数据，填充N/A
            if site_type == 'A_site':
                for i in range(1, 4):
                    properties[f'ionization_energy_{i}'] = 'N/A'
            else:
                for i in range(1, 6):
                    properties[f'ionization_energy_{i}'] = 'N/A'

        # 原子偶极极化率
        properties['dipole_polarizability'] = getattr(elem, 'dipole_polarizability', None)
        if properties['dipole_polarizability'] is not None:
            try:
                properties['dipole_polarizability'] = f"{float(properties['dipole_polarizability']):.3f}"
            except (ValueError, TypeError):
                properties['dipole_polarizability'] = 'N/A'
        else:
            properties['dipole_polarizability'] = 'N/A'

        # 各种电负性 - 这些是方法，需要调用
        electronegativity_methods = {
            'electronegativity_pauling': 'electronegativity_pauling',
            'electronegativity_allen': 'electronegativity_allen',
            'electronegativity_martynov_batsanov': 'electronegativity_martynov_batsanov',
            'electronegativity_ghosh': 'electronegativity_ghosh'
        }

        for key, method_name in electronegativity_methods.items():
            try:
                method = getattr(elem, method_name, None)
                if method is not None and callable(method):
                    value = method()
                    if value is not None:
                        properties[key] = f"{float(value):.3f}"
                    else:
                        properties[key] = 'N/A'
                else:
                    properties[key] = 'N/A'
            except Exception:
                properties[key] = 'N/A'

        return properties

    except Exception as e:
        print(f"获取元素 {element_symbol} 的额外属性时出错: {e}")
        # 返回空值字典
        properties = {
            'covalent_radius_pyykko': 'N/A',
            'electron_affinity': 'N/A',
            'dipole_polarizability': 'N/A',
            'electronegativity_pauling': 'N/A',
            'electronegativity_allen': 'N/A',
            'electronegativity_martynov_batsanov': 'N/A',
            'electronegativity_ghosh': 'N/A'
        }

        # 添加电离能
        if site_type == 'A_site':
            for i in range(1, 4):
                properties[f'ionization_energy_{i}'] = 'N/A'
        else:
            for i in range(1, 6):
                properties[f'ionization_energy_{i}'] = 'N/A'

        return properties

def get_ionic_radius_and_coordination(element_symbol, target_coordination_number, oxidation_states_str):
    """
    获取指定配位数下的离子半径和实际使用的配位数

    Args:
        element_symbol (str): 元素符号
        target_coordination_number (int): 目标配位数 (6 或 12)
        oxidation_states_str (str): 氧化态字符串，如 "2, 3"

    Returns:
        tuple: (离子半径值, 实际配位数), 如果获取失败返回 ('N/A', 'N/A')
    """
    try:
        elem = element(element_symbol)

        if not hasattr(elem, 'ionic_radii') or not elem.ionic_radii:
            return 'N/A', 'N/A'

        # 将配位数转换为字符串格式
        coord_str = {
            4: 'IV',
            5: 'V',
            6: 'VI',
            7: 'VII',
            8: 'VIII',
            9: 'IX',
            10: 'X',
            12: 'XII'
        }.get(target_coordination_number, str(target_coordination_number))

        # 解析氧化态
        if oxidation_states_str == 'N/A':
            possible_charges = []
        else:
            try:
                possible_charges = [int(x.strip()) for x in oxidation_states_str.split(',')]
            except:
                possible_charges = []

        # 查找匹配的离子半径
        best_radius = None
        best_coordination = None
        best_match_score = 0

        for ionic_radius_obj in elem.ionic_radii:
            # 检查配位数是否匹配
            if ionic_radius_obj.coordination == coord_str:
                match_score = 10  # 配位数完全匹配给高分

                # 如果电荷也匹配，增加匹配分数
                if ionic_radius_obj.charge in possible_charges:
                    match_score += 5

                # 如果是最可靠的数据，增加匹配分数
                if ionic_radius_obj.most_reliable:
                    match_score += 2

                # 选择最佳匹配
                if match_score > best_match_score:
                    best_match_score = match_score
                    best_radius = ionic_radius_obj.ionic_radius
                    best_coordination = ionic_radius_obj.coordination

        # 如果没有找到精确匹配的配位数，寻找最接近的配位数
        if best_radius is None:
            # 配位数到数值的映射
            coord_to_num = {
                'I': 1, 'II': 2, 'III': 3, 'IV': 4, 'V': 5, 'VI': 6,
                'VII': 7, 'VIII': 8, 'IX': 9, 'X': 10, 'XI': 11, 'XII': 12
            }

            target_num = target_coordination_number
            closest_distance = float('inf')

            # 首先尝试找电荷匹配且配位数最接近的
            for ionic_radius_obj in elem.ionic_radii:
                if ionic_radius_obj.charge in possible_charges:
                    coord_num = coord_to_num.get(ionic_radius_obj.coordination, 0)
                    distance = abs(coord_num - target_num)

                    match_score = 5  # 电荷匹配基础分
                    if ionic_radius_obj.most_reliable:
                        match_score += 2
                    # 距离越近分数越高
                    match_score += max(0, 10 - distance)

                    if match_score > best_match_score or (match_score == best_match_score and distance < closest_distance):
                        best_match_score = match_score
                        closest_distance = distance
                        best_radius = ionic_radius_obj.ionic_radius
                        best_coordination = ionic_radius_obj.coordination

        # 如果还是没有找到（没有匹配的电荷），选择配位数最接近的
        if best_radius is None and elem.ionic_radii:
            coord_to_num = {
                'I': 1, 'II': 2, 'III': 3, 'IV': 4, 'V': 5, 'VI': 6,
                'VII': 7, 'VIII': 8, 'IX': 9, 'X': 10, 'XI': 11, 'XII': 12
            }

            target_num = target_coordination_number
            closest_distance = float('inf')

            for ionic_radius_obj in elem.ionic_radii:
                coord_num = coord_to_num.get(ionic_radius_obj.coordination, 0)
                distance = abs(coord_num - target_num)

                match_score = 1  # 基础分
                if ionic_radius_obj.most_reliable:
                    match_score += 2

                if distance < closest_distance or (distance == closest_distance and match_score > best_match_score):
                    closest_distance = distance
                    best_match_score = match_score
                    best_radius = ionic_radius_obj.ionic_radius
                    best_coordination = ionic_radius_obj.coordination

        if best_radius is not None:
            return f"{best_radius:.1f}", best_coordination
        else:
            return 'N/A', 'N/A'

    except Exception as e:
        print(f"获取元素 {element_symbol} 的离子半径时出错: {e}")
        return 'N/A', 'N/A'

def add_oxidation_states_to_excel(file_path):
    """
    读取Excel文件，为A_site和B_site两个工作表添加氧化态列
    
    Args:
        file_path (str): Excel文件路径
    """
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"发现工作表: {excel_file.sheet_names}")
        
        # 创建一个字典来存储更新后的数据
        updated_sheets = {}
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n处理工作表: {sheet_name}")
            
            # 读取当前工作表
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"工作表 {sheet_name} 的列: {list(df.columns)}")
            print(f"工作表 {sheet_name} 的形状: {df.shape}")
            
            # 显示前几行数据
            print(f"前5行数据:")
            print(df.head())
            
            # 寻找包含元素符号的列
            element_column = None
            for col in df.columns:
                if any(keyword in str(col).lower() for keyword in ['element', 'symbol', '元素']):
                    element_column = col
                    break
            
            if element_column is None:
                # 如果没有找到明确的元素列，尝试第一列
                element_column = df.columns[0]
                print(f"未找到明确的元素列，使用第一列: {element_column}")
            else:
                print(f"找到元素列: {element_column}")
            
            # 确定配位数
            if sheet_name == 'A_site':
                coordination_number = 12
                print(f"A位元素，配位数: {coordination_number}")
            elif sheet_name == 'B_site':
                coordination_number = 6
                print(f"B位元素，配位数: {coordination_number}")
            else:
                coordination_number = 6  # 默认配位数
                print(f"未知工作表，使用默认配位数: {coordination_number}")

            # 定义所有列名
            column_names = {
                'oxidation_states': 'Oxidation_States',
                'ionic_radius': 'Ionic_Radius_pm',
                'coordination': 'Actual_Coordination',
                'covalent_radius': 'Covalent_Radius_Pyykko_pm',
                'electron_affinity': 'Electron_Affinity_eV',
                'dipole_polarizability': 'Dipole_Polarizability',
                'electronegativity_pauling': 'Electronegativity_Pauling',
                'electronegativity_allen': 'Electronegativity_Allen',
                'electronegativity_martynov_batsanov': 'Electronegativity_Martynov_Batsanov',
                'electronegativity_ghosh': 'Electronegativity_Ghosh'
            }

            # 添加电离能列名（根据位点类型）
            if sheet_name == 'A_site':
                for i in range(1, 4):
                    column_names[f'ionization_energy_{i}'] = f'Ionization_Energy_{i}_eV'
            else:  # B_site
                for i in range(1, 6):
                    column_names[f'ionization_energy_{i}'] = f'Ionization_Energy_{i}_eV'

            # 检查现有列
            for key, col_name in column_names.items():
                if col_name in df.columns:
                    print(f"{col_name} 列已存在，将更新数据")
                else:
                    print(f"添加新列: {col_name}")

            # 为每个元素获取所有属性
            all_data = {key: [] for key in column_names.keys()}

            for _, row in df.iterrows():
                element_symbol = str(row[element_column]).strip()
                if pd.isna(element_symbol) or element_symbol == '' or element_symbol == 'nan':
                    # 为空元素填充N/A
                    for key in all_data.keys():
                        all_data[key].append('N/A')
                else:
                    # 获取氧化态
                    ox_states = get_oxidation_states(element_symbol)
                    all_data['oxidation_states'].append(ox_states)

                    # 获取离子半径和实际配位数
                    ionic_radius, actual_coord = get_ionic_radius_and_coordination(element_symbol, coordination_number, ox_states)
                    all_data['ionic_radius'].append(ionic_radius)
                    all_data['coordination'].append(actual_coord)

                    # 获取额外属性
                    additional_props = get_additional_properties(element_symbol, sheet_name)

                    # 添加额外属性到数据中
                    all_data['covalent_radius'].append(additional_props['covalent_radius_pyykko'])
                    all_data['electron_affinity'].append(additional_props['electron_affinity'])
                    all_data['dipole_polarizability'].append(additional_props['dipole_polarizability'])
                    all_data['electronegativity_pauling'].append(additional_props['electronegativity_pauling'])
                    all_data['electronegativity_allen'].append(additional_props['electronegativity_allen'])
                    all_data['electronegativity_martynov_batsanov'].append(additional_props['electronegativity_martynov_batsanov'])
                    all_data['electronegativity_ghosh'].append(additional_props['electronegativity_ghosh'])

                    # 添加电离能
                    if sheet_name == 'A_site':
                        for i in range(1, 4):
                            all_data[f'ionization_energy_{i}'].append(additional_props[f'ionization_energy_{i}'])
                    else:  # B_site
                        for i in range(1, 6):
                            all_data[f'ionization_energy_{i}'].append(additional_props[f'ionization_energy_{i}'])

                    # 检查是否匹配目标配位数
                    coord_match = "✓" if actual_coord == {6: 'VI', 12: 'XII'}.get(coordination_number, str(coordination_number)) else "✗"
                    print(f"元素 {element_symbol}: 氧化态={ox_states}, 离子半径={ionic_radius} pm, 配位数={actual_coord} {coord_match}")

            # 添加所有列到DataFrame
            for key, col_name in column_names.items():
                df[col_name] = all_data[key]
            
            # 保存更新后的数据框
            updated_sheets[sheet_name] = df
        
        # 将所有工作表写回Excel文件
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            for sheet_name, df in updated_sheets.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"\n成功更新文件: {file_path}")
        
        # 显示更新后的结果
        for sheet_name, df in updated_sheets.items():
            print(f"\n工作表 {sheet_name} 更新后的列: {list(df.columns)}")
            print(f"更新后的前5行数据:")
            print(df.head())
            
    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    file_path = 'element_data.xlsx'
    
    print("开始处理element_data.xlsx文件...")
    print("=" * 50)
    
    # 检查文件是否存在
    import os
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return
    
    # 添加氧化态信息
    add_oxidation_states_to_excel(file_path)
    
    print("=" * 50)
    print("处理完成!")

if __name__ == "__main__":
    main()
