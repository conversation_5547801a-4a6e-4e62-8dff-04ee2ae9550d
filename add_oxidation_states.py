#!/usr/bin/env python3
"""
从mendeleev库中获取元素的氧化态信息，并添加到element_data.xlsx文件中
"""

import pandas as pd
from mendeleev import element
import numpy as np

def get_oxidation_states(element_symbol):
    """
    获取元素的氧化态

    Args:
        element_symbol (str): 元素符号，如 'H', 'He', 'Li' 等

    Returns:
        str: 氧化态列表的字符串表示，如果获取失败返回 'N/A'
    """
    try:
        elem = element(element_symbol)

        # 首先尝试使用 oxistates 属性（这是直接的列表）
        if hasattr(elem, 'oxistates') and elem.oxistates:
            ox_states = elem.oxistates
            ox_states_str = [str(state) for state in ox_states]
            return ', '.join(ox_states_str)

        # 如果没有 oxistates，尝试 oxidation_states 方法
        if hasattr(elem, 'oxidation_states'):
            ox_states = elem.oxidation_states()
            if ox_states and len(ox_states) > 0:
                ox_states_str = [str(state) for state in ox_states]
                return ', '.join(ox_states_str)

        # 如果都没有，返回 N/A
        return 'N/A'

    except Exception as e:
        print(f"获取元素 {element_symbol} 的氧化态时出错: {e}")
        return 'N/A'

def get_ionic_radius_and_coordination(element_symbol, target_coordination_number, oxidation_states_str):
    """
    获取指定配位数下的离子半径和实际使用的配位数

    Args:
        element_symbol (str): 元素符号
        target_coordination_number (int): 目标配位数 (6 或 12)
        oxidation_states_str (str): 氧化态字符串，如 "2, 3"

    Returns:
        tuple: (离子半径值, 实际配位数), 如果获取失败返回 ('N/A', 'N/A')
    """
    try:
        elem = element(element_symbol)

        if not hasattr(elem, 'ionic_radii') or not elem.ionic_radii:
            return 'N/A', 'N/A'

        # 将配位数转换为字符串格式
        coord_str = {
            4: 'IV',
            5: 'V',
            6: 'VI',
            7: 'VII',
            8: 'VIII',
            9: 'IX',
            10: 'X',
            12: 'XII'
        }.get(target_coordination_number, str(target_coordination_number))

        # 解析氧化态
        if oxidation_states_str == 'N/A':
            possible_charges = []
        else:
            try:
                possible_charges = [int(x.strip()) for x in oxidation_states_str.split(',')]
            except:
                possible_charges = []

        # 查找匹配的离子半径
        best_radius = None
        best_coordination = None
        best_match_score = 0
        best_obj = None

        for ionic_radius_obj in elem.ionic_radii:
            # 检查配位数是否匹配
            if ionic_radius_obj.coordination == coord_str:
                match_score = 10  # 配位数完全匹配给高分

                # 如果电荷也匹配，增加匹配分数
                if ionic_radius_obj.charge in possible_charges:
                    match_score += 5

                # 如果是最可靠的数据，增加匹配分数
                if ionic_radius_obj.most_reliable:
                    match_score += 2

                # 选择最佳匹配
                if match_score > best_match_score:
                    best_match_score = match_score
                    best_radius = ionic_radius_obj.ionic_radius
                    best_coordination = ionic_radius_obj.coordination
                    best_obj = ionic_radius_obj

        # 如果没有找到精确匹配的配位数，尝试找最接近的
        if best_radius is None:
            for ionic_radius_obj in elem.ionic_radii:
                if ionic_radius_obj.charge in possible_charges:
                    match_score = 3  # 电荷匹配但配位数不匹配
                    if ionic_radius_obj.most_reliable:
                        match_score += 2

                    if match_score > best_match_score:
                        best_match_score = match_score
                        best_radius = ionic_radius_obj.ionic_radius
                        best_coordination = ionic_radius_obj.coordination
                        best_obj = ionic_radius_obj

        # 如果还是没有找到，选择第一个可用的
        if best_radius is None and elem.ionic_radii:
            best_obj = elem.ionic_radii[0]
            best_radius = best_obj.ionic_radius
            best_coordination = best_obj.coordination

        if best_radius is not None:
            return f"{best_radius:.1f}", best_coordination
        else:
            return 'N/A', 'N/A'

    except Exception as e:
        print(f"获取元素 {element_symbol} 的离子半径时出错: {e}")
        return 'N/A', 'N/A'

def add_oxidation_states_to_excel(file_path):
    """
    读取Excel文件，为A_site和B_site两个工作表添加氧化态列
    
    Args:
        file_path (str): Excel文件路径
    """
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"发现工作表: {excel_file.sheet_names}")
        
        # 创建一个字典来存储更新后的数据
        updated_sheets = {}
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n处理工作表: {sheet_name}")
            
            # 读取当前工作表
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"工作表 {sheet_name} 的列: {list(df.columns)}")
            print(f"工作表 {sheet_name} 的形状: {df.shape}")
            
            # 显示前几行数据
            print(f"前5行数据:")
            print(df.head())
            
            # 寻找包含元素符号的列
            element_column = None
            for col in df.columns:
                if any(keyword in str(col).lower() for keyword in ['element', 'symbol', '元素']):
                    element_column = col
                    break
            
            if element_column is None:
                # 如果没有找到明确的元素列，尝试第一列
                element_column = df.columns[0]
                print(f"未找到明确的元素列，使用第一列: {element_column}")
            else:
                print(f"找到元素列: {element_column}")
            
            # 确定配位数
            if sheet_name == 'A_site':
                coordination_number = 12
                print(f"A位元素，配位数: {coordination_number}")
            elif sheet_name == 'B_site':
                coordination_number = 6
                print(f"B位元素，配位数: {coordination_number}")
            else:
                coordination_number = 6  # 默认配位数
                print(f"未知工作表，使用默认配位数: {coordination_number}")

            # 检查是否已经存在相关列
            oxidation_column_name = 'Oxidation_States'
            ionic_radius_column_name = 'Ionic_Radius_pm'
            coordination_column_name = 'Actual_Coordination'

            if oxidation_column_name in df.columns:
                print(f"氧化态列已存在，将更新数据")
            else:
                print(f"添加新的氧化态列: {oxidation_column_name}")

            if ionic_radius_column_name in df.columns:
                print(f"离子半径列已存在，将更新数据")
            else:
                print(f"添加新的离子半径列: {ionic_radius_column_name}")

            if coordination_column_name in df.columns:
                print(f"实际配位数列已存在，将更新数据")
            else:
                print(f"添加新的实际配位数列: {coordination_column_name}")

            # 为每个元素获取氧化态、离子半径和实际配位数
            oxidation_states = []
            ionic_radii = []
            actual_coordinations = []

            for _, row in df.iterrows():
                element_symbol = str(row[element_column]).strip()
                if pd.isna(element_symbol) or element_symbol == '' or element_symbol == 'nan':
                    oxidation_states.append('N/A')
                    ionic_radii.append('N/A')
                    actual_coordinations.append('N/A')
                else:
                    # 获取氧化态
                    ox_states = get_oxidation_states(element_symbol)
                    oxidation_states.append(ox_states)

                    # 获取离子半径和实际配位数
                    ionic_radius, actual_coord = get_ionic_radius_and_coordination(element_symbol, coordination_number, ox_states)
                    ionic_radii.append(ionic_radius)
                    actual_coordinations.append(actual_coord)

                    # 检查是否匹配目标配位数
                    coord_match = "✓" if actual_coord == {6: 'VI', 12: 'XII'}.get(coordination_number, str(coordination_number)) else "✗"
                    print(f"元素 {element_symbol}: 氧化态={ox_states}, 离子半径={ionic_radius} pm, 目标配位数={coordination_number}, 实际配位数={actual_coord} {coord_match}")

            # 添加所有列
            df[oxidation_column_name] = oxidation_states
            df[ionic_radius_column_name] = ionic_radii
            df[coordination_column_name] = actual_coordinations
            
            # 保存更新后的数据框
            updated_sheets[sheet_name] = df
        
        # 将所有工作表写回Excel文件
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            for sheet_name, df in updated_sheets.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"\n成功更新文件: {file_path}")
        
        # 显示更新后的结果
        for sheet_name, df in updated_sheets.items():
            print(f"\n工作表 {sheet_name} 更新后的列: {list(df.columns)}")
            print(f"更新后的前5行数据:")
            print(df.head())
            
    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    file_path = 'element_data.xlsx'
    
    print("开始处理element_data.xlsx文件...")
    print("=" * 50)
    
    # 检查文件是否存在
    import os
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return
    
    # 添加氧化态信息
    add_oxidation_states_to_excel(file_path)
    
    print("=" * 50)
    print("处理完成!")

if __name__ == "__main__":
    main()
